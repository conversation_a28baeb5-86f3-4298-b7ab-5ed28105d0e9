---
title: AI SDK UI
description: Learn about the AI SDK UI.
---

# AI SDK UI

<IndexCards
  cards={[
    {
      title: 'Overview',
      description: 'Get an overview about the AI SDK UI.',
      href: '/docs/ai-sdk-ui/overview'
    },
    {
      title: 'Chatbot',
      description: 'Learn how to integrate an interface for a chatbot.',
      href: '/docs/ai-sdk-ui/chatbot'
    },
    {
      title: 'Chatbot Message Persistence',
      description: 'Learn how to store and load chat messages in a chatbot.',
      href: '/docs/ai-sdk-ui/chatbot-message-persistence'
    },
    {
      title: 'Chatbot Tool Usage',
      description:
        'Learn how to integrate an interface for a chatbot with tool calling.',
      href: '/docs/ai-sdk-ui/chatbot-tool-usage'
    },
    {
      title: 'Completion',
      description: 'Learn how to integrate an interface for text completion.',
      href: '/docs/ai-sdk-ui/completion'
    },
    {
      title: 'Object Generation',
      description: 'Learn how to integrate an interface for object generation.',
      href: '/docs/ai-sdk-ui/object-generation'
    },
    {
      title: 'Streaming Data',
      description: 'Learn how to stream data.',
      href: '/docs/ai-sdk-ui/streaming-data'
    },
    {
      title: 'Reading UI Message Streams',
      description: 'Learn how to read UIMessage streams for terminal UIs, custom clients, and server components.',
      href: '/docs/ai-sdk-ui/reading-ui-message-streams'
    },
    {
      title: 'Error Handling',
      description: 'Learn how to handle errors.',
      href: '/docs/ai-sdk-ui/error-handling'
    },
    {
      title: 'Stream Protocol',
      description:
        'The stream protocol defines how data is sent from the backend to the AI SDK UI frontend.',
      href: '/docs/ai-sdk-ui/stream-protocol'
    }

]}
/>
