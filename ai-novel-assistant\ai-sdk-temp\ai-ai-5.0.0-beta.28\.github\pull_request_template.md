<!--
Welcome to contributing to AI SDK! We're excited to see your changes.

We suggest you read the following contributing guide we've created before submitting:

https://github.com/vercel/ai/blob/main/CONTRIBUTING.md
-->

## Background

<!-- Why was this change necessary? -->

## Summary

<!-- What did you change? -->

## Verification

<!--
For features & bugfixes.
Please explain how you *manually* verified that the change works end-to-end as expected (independent of automated tests).
Remove the section if it's not needed (e.g. for docs).
-->

## Tasks

<!--
This task list is intended to help you keep track of what you need to do.
Feel free to add tasks and remove unnecessary tasks as needed.

Please check if the PR fulfills the following requirements:
-->

- [ ] Tests have been added / updated (for bug fixes / features)
- [ ] Documentation has been added / updated (for bug fixes / features)
- [ ] A _patch_ changeset for relevant packages has been added (for bug fixes / features - run `pnpm changeset` in the project root)
- [ ] Formatting issues have been fixed (run `pnpm prettier-fix` in the project root)

## Future Work

<!--
Feel free to mention things not covered by this PR that can be done in future PRs.
Remove the section if it's not needed.
 -->

## Related Issues

<!--
List related issues here, e.g. "Fixes #1234".
Remove the section if it's not needed.
-->
