{"mode": "pre", "tag": "beta", "initialVersions": {"@example/ai-core": "0.0.0", "@example/express": "0.0.0", "@example/fastify": "0.0.0", "@example/hono": "0.0.0", "@example/mcp": "0.0.0", "@example/nest": "0.0.0", "@example/next": "0.0.0", "@example/next-fastapi": "0.0.0", "@example/next-google-vertex": "0.0.0", "@example/next-langchain": "0.0.0", "@example/next-openai": "0.0.0", "@example/next-openai-kasada-bot-protection": "0.0.0", "@example/next-openai-pages": "0.0.0", "@example/next-openai-telemetry": "0.0.0", "@example/next-openai-telemetry-sentry": "0.0.0", "@example/next-openai-rate-limits": "0.0.0", "@example/node-http-server": "0.0.0", "@example/nuxt-openai": "0.0.0", "@example/sveltekit-openai": "0.0.0", "ai": "5.0.0-alpha.15", "@ai-sdk/amazon-bedrock": "3.0.0-alpha.15", "@ai-sdk/anthropic": "2.0.0-alpha.15", "@ai-sdk/assemblyai": "1.0.0-alpha.15", "@ai-sdk/azure": "2.0.0-alpha.15", "@ai-sdk/cerebras": "1.0.0-alpha.15", "@ai-sdk/codemod": "2.0.0-alpha.0", "@ai-sdk/cohere": "2.0.0-alpha.15", "@ai-sdk/deepgram": "1.0.0-alpha.15", "@ai-sdk/deepinfra": "1.0.0-alpha.15", "@ai-sdk/deepseek": "1.0.0-alpha.15", "@ai-sdk/elevenlabs": "1.0.0-alpha.15", "@ai-sdk/fal": "1.0.0-alpha.15", "@ai-sdk/fireworks": "1.0.0-alpha.15", "@ai-sdk/gateway": "1.0.0-alpha.15", "@ai-sdk/gladia": "1.0.0-alpha.15", "@ai-sdk/google": "2.0.0-alpha.15", "@ai-sdk/google-vertex": "3.0.0-alpha.15", "@ai-sdk/groq": "2.0.0-alpha.15", "@ai-sdk/hume": "1.0.0-alpha.15", "@ai-sdk/langchain": "1.0.0-alpha.15", "@ai-sdk/llamaindex": "1.0.0-alpha.15", "@ai-sdk/lmnt": "1.0.0-alpha.15", "@ai-sdk/luma": "1.0.0-alpha.15", "@ai-sdk/mistral": "2.0.0-alpha.15", "@ai-sdk/openai": "2.0.0-alpha.15", "@ai-sdk/openai-compatible": "1.0.0-alpha.15", "@ai-sdk/perplexity": "2.0.0-alpha.15", "@ai-sdk/provider": "2.0.0-alpha.15", "@ai-sdk/provider-utils": "3.0.0-alpha.15", "@ai-sdk/react": "2.0.0-alpha.15", "@ai-sdk/replicate": "1.0.0-alpha.15", "@ai-sdk/revai": "1.0.0-alpha.15", "@ai-sdk/rsc": "1.0.0-alpha.15", "ai-core-e2e-next-server": "0.0.0", "@ai-sdk/svelte": "3.0.0-alpha.15", "@ai-sdk/togetherai": "1.0.0-alpha.15", "@ai-sdk/valibot": "1.0.0-alpha.15", "@ai-sdk/vercel": "1.0.0-alpha.15", "@ai-sdk/vue": "2.0.0-alpha.15", "@ai-sdk/xai": "2.0.0-alpha.15", "analyze-downloads": "0.0.0", "eslint-config-vercel-ai": "0.0.0", "generate-llms-txt": "0.0.0", "@vercel/ai-tsconfig": "0.0.0", "@example/angular": "0.0.0", "@ai-sdk/angular": "0.2.1"}, "changesets": ["afraid-countries-crash", "afraid-moles-cross", "angry-crabs-develop", "angry-dragons-tan", "angry-kings-dance", "angry-plants-sin", "angry-poems-learn", "angry-timers-drive", "beige-drinks-punch", "beige-ligers-kneel", "beige-penguins-greet", "beige-socks-stare", "beige-vans-care", "big-impalas-grin", "big-panthers-judge", "brave-mails-taste", "brave-numbers-drive", "bright-chefs-lick", "bright-lies-explode", "bright-readers-breathe", "bright-turtles-give", "brown-eagles-tickle", "brown-geckos-tell", "brown-poems-boil", "calm-boats-complain", "calm-dragons-drive", "calm-eels-obey", "chatty-ladybugs-nail", "chatty-steaks-search", "chilled-clocks-brush", "chilled-queens-remember", "chilly-chairs-press", "chilly-teachers-brush", "chilly-tips-know", "clean-ants-brake", "clean-cheetahs-pump", "clean-numbers-cover", "clever-coats-invite", "clever-games-report", "clever-mangos-tease", "clever-moles-arrive", "clever-terms-lay", "cold-bags-move", "cold-clocks-learn", "cool-buckets-shout", "cool-bulldogs-fix", "cool-gifts-film", "cool-shrimps-kick", "cuddly-eels-perform", "cuddly-icons-kick", "cuddly-kangaroos-double", "cuddly-parrots-float", "cuddly-phones-smell", "cuddly-tigers-confess", "curly-bats-build", "curly-peaches-clap", "curvy-cats-kneel", "curvy-lobsters-share", "curvy-sloths-impress", "cyan-insects-count", "cyan-rockets-beg", "cyan-scissors-applaud", "dirty-dolphins-march", "dirty-eggs-breathe", "dirty-mice-knock", "dull-candles-trade", "dull-grapes-remember", "dull-points-mate", "dull-rabbits-tie", "early-parrots-mix", "eight-emus-push", "eight-months-sip", "eighty-flowers-design", "eighty-planets-drum", "eighty-pugs-sip", "eighty-seals-search", "eleven-lobsters-rescue", "eleven-pets-clean", "eleven-ravens-brake", "empty-fireants-learn", "empty-flowers-sniff", "empty-pets-jump", "empty-schools-cheer", "empty-walls-rest", "fair-bikes-hear", "fair-cobras-tan", "fair-cups-travel", "fair-swans-kneel", "famous-eggs-camp", "famous-fans-provide", "famous-needles-fold", "famous-peas-arrive", "famous-shrimps-fail", "famous-ties-train", "fast-students-turn", "fast-toys-dream", "few-jobs-mate", "few-kangaroos-remember", "few-pianos-pay", "fifty-camels-visit", "fifty-shrimps-kick", "five-grapes-live", "five-ravens-hammer", "fix-env-mutation", "flat-ants-draw", "flat-falcons-happen", "flat-plums-bake", "fluffy-pets-pump", "forty-kangaroos-pull", "four-gorillas-sell", "fresh-dolphins-serve", "fresh-forks-punch", "fresh-jeans-occur", "fresh-otters-chew", "fresh-swans-march", "friendly-otters-sneeze", "funny-cows-sin", "funny-mayflies-yawn", "fuzzy-actors-sneeze", "fuzzy-comics-listen", "fuzzy-goats-mate", "fuzzy-lies-explain", "fuzzy-shoes-act", "gentle-gorillas-mate", "gentle-hairs-study", "gentle-mayflies-call", "gentle-toys-smile", "giant-eyes-relax", "giant-ravens-reflect", "gold-planes-cheer", "gold-weeks-turn", "good-cycles-matter", "good-masks-itch", "good-steaks-serve", "good-students-sin", "good-swans-heal", "good-trains-breathe", "gorgeous-carrots-reflect", "gorgeous-gifts-drop", "great-hats-hide", "great-mangos-scream", "great-poets-attack", "green-deers-scream", "green-dogs-kick", "green-grapes-rhyme", "green-olives-hope", "green-pots-collect", "grumpy-apples-cheat", "happy-ads-happen", "happy-countries-dream", "happy-kangaroos-roll", "healthy-humans-burn", "heavy-ducks-join", "heavy-ligers-lay", "heavy-pens-destroy", "heavy-teachers-repeat", "hip-eagles-attend", "hip-paws-fly", "hip-rocks-mix", "hot-colts-hear", "hot-singers-help", "huge-cloths-burn", "hungry-bears-glow", "hungry-frogs-eat", "hungry-frogs-raise", "hungry-hotels-hunt", "hungry-nails-return", "hungry-pets-hear", "hungry-trains-compete", "hungry-zebras-applaud", "itchy-bats-breathe", "itchy-cars-relax", "itchy-deers-jog", "itchy-pumas-wave", "khaki-bears-drop", "khaki-crabs-reply", "khaki-sheep-sparkle", "khaki-tomatoes-think", "kind-icons-thank", "large-peas-eat", "large-ties-own", "late-brooms-suffer", "late-foxes-battle", "late-socks-flash", "lazy-beans-marry", "lazy-ducks-cheat", "lemon-actors-invite", "lemon-terms-hug", "lemon-yaks-move", "light-books-poke", "light-paws-divide", "light-rules-film", "little-bobcats-jog", "little-carrots-speak", "little-tips-occur", "little-zebras-suffer", "long-dancers-wait", "lovely-boxes-scream", "lovely-chicken-speak", "lovely-garlics-promise", "many-beans-exercise", "many-jobs-explain", "many-toes-glow", "mean-files-talk", "mean-monkeys-sip", "mean-rice-deny", "metal-insects-tease", "metal-masks-wonder", "mighty-rats-jog", "mighty-spies-warn", "modern-grapes-glow", "modern-kings-smoke", "modern-trains-reflect", "moody-rings-remember", "moody-yaks-love", "nasty-avocados-sort", "nasty-cows-care", "nasty-eggs-learn", "nasty-lobsters-shave", "nasty-queens-play", "nasty-spiders-sparkle", "nasty-trains-beg", "neat-frogs-shop", "neat-pillows-occur", "nervous-maps-fix", "new-pens-remain", "new-rats-fly", "new-vans-obey", "nice-dogs-flow", "nice-jars-film", "nice-tips-walk", "nine-falcons-yell", "nine-jars-hammer", "nine-pillows-hug", "nine-rivers-compete", "ninety-buses-bake", "ninety-cameras-wonder", "ninety-seahorses-fetch", "odd-peaches-beam", "odd-tables-cross", "odd-vans-suffer", "old-moons-kiss", "old-yaks-pull", "olive-candles-compare", "olive-ducks-carry", "olive-wombats-pretend", "orange-bags-stare", "orange-clocks-type", "perfect-bottles-ring", "perfect-deers-work", "pink-deers-switch", "pink-mangos-tickle", "plenty-bears-run", "plenty-dingos-bow", "plenty-radios-travel", "poor-bees-do", "poor-bobcats-sort", "poor-kids-lick", "poor-walls-arrive", "popular-plums-begin", "pretty-bikes-appear", "pretty-doors-promise", "pretty-houses-whisper", "pretty-jars-reflect", "pretty-plants-watch", "pretty-pugs-eat", "proud-buckets-guess", "proud-cows-bathe", "proud-dancers-doubt", "purple-dodos-burn", "purple-rocks-cover", "quick-grapes-walk", "quick-toys-help", "quiet-glasses-double", "quiet-peas-end", "rare-foxes-build", "rare-foxes-rest", "rare-hairs-type", "real-apes-lick", "real-bags-tickle", "real-fireants-smell", "real-games-chew", "real-kiwis-collect", "red-berries-report", "red-frogs-cheer", "red-worms-help", "rich-days-call", "rich-frogs-walk", "rotten-birds-promise", "rotten-boats-doubt", "rotten-peaches-doubt", "rotten-tomatoes-smoke", "rotten-walls-provide", "rude-badgers-roll", "rude-beers-remain", "rude-bugs-run", "rude-jobs-occur", "rude-rivers-hide", "selfish-cups-provide", "selfish-masks-jog", "selfish-rice-own", "selfish-wasps-applaud", "serious-clouds-cheer", "serious-numbers-teach", "serious-ravens-stare", "serious-taxis-invent", "serious-trains-raise", "seven-beans-push", "seven-dancers-crash", "seven-fans-speak", "seven-hornets-peel", "seven-pens-itch", "seven-tools-type", "shaggy-experts-warn", "shaggy-singers-promise", "shaggy-tips-crash", "shaggy-toes-watch", "sharp-apes-tickle", "sharp-mangos-relate", "sharp-ties-kneel", "sharp-walls-rush", "shiny-dolphins-lie", "shiny-parents-know", "shy-hotels-allow", "shy-lamps-visit", "shy-plants-serve", "silent-nails-taste", "silent-paws-decide", "silly-brooms-thank", "silver-laws-play", "silver-vans-march", "six-garlics-sin", "six-guests-sleep", "six-moose-know", "six-olives-rest", "six-shrimps-attack", "slimy-apples-film", "slimy-chefs-play", "slimy-feet-fail", "slimy-kangaroos-sort", "slow-donuts-study", "slow-laws-end", "slow-pants-buy", "slow-students-sleep", "slow-windows-ring", "small-deers-cover", "small-files-pump", "small-flies-greet", "smart-keys-check", "smart-llamas-itch", "smart-singers-remain", "smart-swans-drive", "smooth-bikes-trade", "smooth-carpets-bathe", "smooth-mirrors-kneel", "soft-forks-poke", "sour-bananas-remain", "sour-mails-cheer", "sour-mails-clap", "sour-papayas-end", "sour-radios-boil", "sour-rockets-greet", "sour-scissors-teach", "sour-snails-admire", "sour-trains-remember", "spicy-bats-impress", "spicy-bears-kick", "spicy-mangos-brush", "spicy-shoes-matter", "spotty-apples-call", "spotty-countries-appear", "spotty-dolls-divide", "spotty-feet-share", "spotty-goats-confess", "spotty-swans-know", "stale-bats-prove", "stale-cherries-heal", "stale-rockets-hope", "stale-tools-exercise", "strange-apricots-enjoy", "strange-camels-decide", "strange-flies-remember", "strange-maps-compete", "strong-readers-notice", "strong-windows-wave", "stupid-pots-laugh", "stupid-wasps-pump", "sweet-flowers-smash", "sweet-lobsters-type", "sweet-olives-rescue", "sweet-turtles-kiss", "swift-countries-applaud", "swift-geckos-joke", "swift-ghosts-itch", "swift-needles-sniff", "swift-turtles-rhyme", "tall-deers-obey", "tall-garlics-sit", "tall-rice-flash", "tall-sheep-hammer", "tame-doors-hammer", "tasty-starfishes-swim", "ten-ligers-turn", "ten-masks-run", "ten-students-yell", "ten-windows-serve", "ten-zebras-speak", "tender-ads-drop", "tender-buses-glow", "tender-comics-rescue", "tender-lizards-switch", "tender-tables-trade", "thick-chairs-remain", "thick-melons-talk", "thick-parents-grab", "thin-items-knock", "thin-numbers-shave", "thin-zoos-type", "thirty-hornets-thank", "three-fishes-applaud", "three-jars-fix", "three-pans-move", "tidy-sheep-grin", "tiny-deers-kick", "tiny-seals-flash", "tough-islands-sniff", "tough-mirrors-peel", "tough-mugs-fail", "tough-pugs-drive", "tough-suns-eat", "tough-suns-learn", "tricky-baboons-switch", "tricky-hats-fly", "tricky-lions-deliver", "tricky-ravens-kick", "tricky-snakes-leave", "tricky-zebras-cover", "twelve-baboons-sing", "twelve-kids-travel", "twelve-pianos-destroy", "twelve-stingrays-behave", "twelve-waves-stare", "two-otters-divide", "two-otters-whisper", "two-roses-think", "unlucky-bikes-fry", "unlucky-bobcats-wash", "unlucky-cherries-rescue", "unlucky-kiwis-build", "unlucky-toes-laugh", "violet-taxis-work", "warm-eagles-play", "weak-bikes-warn", "weak-moles-nail", "weak-rice-wait", "wet-meals-pretend", "wet-toys-burn", "wet-trainers-vanish", "wicked-flowers-study", "wicked-snakes-march", "wild-beans-help", "wild-candles-judge", "wild-cats-work", "wild-hairs-hide", "wild-pugs-burn", "wild-tables-enjoy", "wise-gorillas-act", "wise-headers-respect", "witty-candles-pretend", "yellow-chefs-kick", "yellow-eels-sort", "yellow-ligers-brake", "young-dingos-march", "young-dolls-yell", "young-dragons-run", "young-sheep-roll"]}