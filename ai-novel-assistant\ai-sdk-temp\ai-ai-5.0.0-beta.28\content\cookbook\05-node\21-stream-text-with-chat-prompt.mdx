---
title: Stream Text with Chat Prompt
description: Learn how to stream text with chat prompt using the AI SDK and Node
tags: ['node', 'streaming', 'chat']
---

# Stream Text with Chat Prompt

Text generation can sometimes take a long time to finish, especially when the response is big.
In such cases, it is useful to stream the chat completion to the client in real-time.
This allows the client to display the new message as it is being generated by the model,
rather than have users wait for it to finish.

```ts file='index.ts'
import { streamText } from 'ai';
import { openai } from '@ai-sdk/openai';

const result = streamText({
  model: openai('gpt-4o'),
  maxOutputTokens: 1024,
  system: 'You are a helpful chatbot.',
  messages: [
    {
      role: 'user',
      content: [{ type: 'text', text: 'Hello!' }],
    },
    {
      role: 'assistant',
      content: [{ type: 'text', text: 'Hello! How can I help you today?' }],
    },
    {
      role: 'user',
      content: [{ type: 'text', text: 'I need help with my computer.' }],
    },
  ],
});

for await (const textPart of result.textStream) {
  process.stdout.write(textPart);
}
```
